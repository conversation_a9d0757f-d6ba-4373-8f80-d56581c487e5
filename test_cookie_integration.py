#!/usr/bin/env python3
"""
Test Cookie Integration
测试Cookie集成

This script tests whether the cookie allocation mechanism is properly implemented
此脚本测试Cookie分配机制是否正确实现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_user_server_cookie():
    """Test if UserInfoServer uses user task cookie"""
    print("=== Testing User Server Cookie Integration ===")
    
    try:
        from server.user_info_till_server import UserInfoServer
        from server.cookie_manager import get_task_cookie
        
        # Get user task cookie
        user_cookie = get_task_cookie('user')
        print(f"User task cookie available: {user_cookie is not None}")
        if user_cookie:
            print(f"User cookie (first 50 chars): {user_cookie[:50]}...")
        
        # Create a test user server instance
        test_server = UserInfoServer('test_char')
        
        # Check if it uses the correct credential
        if hasattr(test_server, 'credential'):
            print(f"Server has credential: True")
            print(f"SESSDATA (first 20 chars): {test_server.credential.sessdata[:20]}...")
            print(f"bili_jct (first 10 chars): {test_server.credential.bili_jct[:10]}...")
        else:
            print("Server has credential: False")
            
        return True
        
    except Exception as e:
        print(f"Error testing user server: {e}")
        return False


def test_creator_server_cookie():
    """Test if CreatorInfoServer uses creator task cookie"""
    print("\n=== Testing Creator Server Cookie Integration ===")
    
    try:
        from server.creator_info_server import CreatorInfoServer
        from server.cookie_manager import get_task_cookie
        
        # Get creator task cookie
        creator_cookie = get_task_cookie('creator')
        print(f"Creator task cookie available: {creator_cookie is not None}")
        if creator_cookie:
            print(f"Creator cookie (first 50 chars): {creator_cookie[:50]}...")
        
        # Create a test creator server instance
        test_server = CreatorInfoServer()
        
        # Check if it uses the correct credential
        if hasattr(test_server, 'creator_credential'):
            print(f"Server has creator_credential: True")
            print(f"SESSDATA (first 20 chars): {test_server.creator_credential.sessdata[:20]}...")
            print(f"bili_jct (first 10 chars): {test_server.creator_credential.bili_jct[:10]}...")
        else:
            print("Server has creator_credential: False")
            
        return True
        
    except Exception as e:
        print(f"Error testing creator server: {e}")
        return False


def test_live_cookie():
    """Test if live monitoring uses live task cookie"""
    print("\n=== Testing Live Cookie Integration ===")
    
    try:
        from server.cookie_manager import get_task_cookie
        
        # Get live task cookie
        live_cookie = get_task_cookie('live')
        print(f"Live task cookie available: {live_cookie is not None}")
        if live_cookie:
            print(f"Live cookie (first 50 chars): {live_cookie[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"Error testing live cookie: {e}")
        return False


def test_cookie_manager_status():
    """Test cookie manager status"""
    print("\n=== Testing Cookie Manager Status ===")
    
    try:
        from server.cookie_manager import get_cookie_manager
        
        manager = get_cookie_manager()
        status = manager.get_status()
        
        print(f"Enabled tasks: {status['enabled_tasks']}")
        
        for task_type, cookie_status in status['cookies'].items():
            print(f"\nTask: {task_type}")
            print(f"  Enabled: {cookie_status['enabled']}")
            print(f"  Has refresh token: {cookie_status['has_refresh_token']}")
            print(f"  Needs refresh: {cookie_status['needs_refresh']}")
        
        return True
        
    except Exception as e:
        print(f"Error testing cookie manager: {e}")
        return False


def test_cookie_isolation():
    """Test if different tasks use different cookies"""
    print("\n=== Testing Cookie Isolation ===")
    
    try:
        from server.cookie_manager import get_task_cookie
        
        user_cookie = get_task_cookie('user')
        creator_cookie = get_task_cookie('creator')
        live_cookie = get_task_cookie('live')
        
        print(f"User cookie available: {user_cookie is not None}")
        print(f"Creator cookie available: {creator_cookie is not None}")
        print(f"Live cookie available: {live_cookie is not None}")
        
        # Check if they are different (if both available)
        if user_cookie and creator_cookie:
            print(f"User and Creator cookies are different: {user_cookie != creator_cookie}")
        
        if user_cookie and live_cookie:
            print(f"User and Live cookies are different: {user_cookie != live_cookie}")
            
        if creator_cookie and live_cookie:
            print(f"Creator and Live cookies are different: {creator_cookie != live_cookie}")
        
        return True
        
    except Exception as e:
        print(f"Error testing cookie isolation: {e}")
        return False


def main():
    """Main test function"""
    print("Cookie Integration Test")
    print("=" * 50)
    
    tests = [
        test_cookie_manager_status,
        test_user_server_cookie,
        test_creator_server_cookie,
        test_live_cookie,
        test_cookie_isolation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"Passed: {sum(results)}/{len(results)}")
    
    if all(results):
        print("✅ All tests passed! Cookie integration is working correctly.")
    else:
        print("❌ Some tests failed. Cookie integration needs fixes.")
    
    return all(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

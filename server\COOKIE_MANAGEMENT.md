# Cookie管理系统使用说明

## 概述

本系统实现了为三个不同的数据拉取任务分别分配独立的Cookie，并提供自动更新功能，以避免触发B站的风控机制。

## 功能特性

1. **Cookie分配机制**：为user、creator、live三个任务分别分配独立的Cookie
2. **自动更新功能**：基于B站官方API实现Cookie的定时自动更新
3. **错误处理**：完整的错误处理和重试机制
4. **命令行工具**：提供便捷的Cookie管理命令行界面

## 系统架构

### 核心组件

- `cookie_manager.py`: Cookie管理核心类
- `cookie_refresh.py`: Cookie刷新机制实现
- `cookie_adapter.py`: 为现有服务器提供Cookie适配
- `cookie_config.json`: Cookie配置文件
- `cookie_cli.py`: 命令行管理工具

### 任务类型

- **user**: 用户数据收集任务（粉丝数、动态、视频等）
- **creator**: 创作者数据收集任务（现有creator_info_server功能）
- **live**: 直播数据收集任务（直播状态、弹幕等）

## 配置说明

### Cookie配置文件 (cookie_config.json)

```json
{
  "cookies": {
    "user": {
      "description": "Cookie for user data collection tasks",
      "SESSDATA": "your_sessdata_here",
      "bili_jct": "your_bili_jct_here",
      "buvid3": "your_buvid3_here",
      "buvid4": "your_buvid4_here", 
      "DedeUserID": "your_dedeuserid_here",
      "b_nut": "your_b_nut_here",
      "sid": "your_sid_here",
      "refresh_token": "your_refresh_token_here",
      "last_refresh": null,
      "enabled": true
    }
  },
  "refresh_settings": {
    "check_interval_hours": 24,
    "retry_count": 3,
    "retry_delay_seconds": 30,
    "refresh_before_expire_hours": 72
  }
}
```

### 刷新设置说明

- `check_interval_hours`: Cookie检查间隔（小时）
- `retry_count`: 刷新失败重试次数
- `retry_delay_seconds`: 重试延迟（秒）
- `refresh_before_expire_hours`: 提前刷新时间（小时）

## 使用方法

### 1. 安装依赖

```bash
pip install pycryptodome
```

### 2. 配置Cookie

#### 方法一：使用命令行工具

```bash
# 查看状态
python -m server cookie status

# 设置Cookie（包含refresh_token）
python -m server cookie set user "SESSDATA=xxx;bili_jct=xxx;buvid3=xxx;..." "refresh_token_here"

# 启用任务
python -m server cookie enable user

# 测试Cookie
python -m server cookie test user
```

#### 方法二：直接编辑配置文件

编辑 `server/cookie_config.json` 文件，填入相应的Cookie信息。

### 3. 获取Cookie和refresh_token

#### 获取Cookie

1. 登录B站网页版
2. 打开浏览器开发者工具 (F12)
3. 在Network标签页中找到任意请求
4. 复制Cookie字段的值

#### 获取refresh_token

refresh_token通常在登录成功后返回，需要从以下位置获取：
- 浏览器localStorage中的`ac_time_value`字段
- 登录API响应中的`refresh_token`字段

### 4. 启动系统

系统会在unified_data_scheduler初始化时自动启动Cookie管理：

```python
from server.unified_data_scheduler import UnifiedDataScheduler

scheduler = UnifiedDataScheduler()
await scheduler.initialize()  # 自动启动Cookie管理
```

## Cookie刷新机制

### 刷新流程

1. **检查是否需要刷新**：调用B站API检查Cookie状态
2. **生成CorrespondPath**：使用RSA-OAEP加密生成签名
3. **获取refresh_csrf**：从B站获取实时刷新令牌
4. **执行刷新**：使用refresh_token和refresh_csrf刷新Cookie
5. **确认更新**：确认刷新成功并使旧token失效

### 自动刷新

- 系统每24小时检查一次Cookie状态
- 如果需要刷新，会自动执行刷新流程
- 刷新成功后更新配置文件

### 手动刷新

```bash
# 刷新特定任务的Cookie
python -m server cookie refresh user

# 刷新所有需要刷新的Cookie
python -m server cookie refresh-all
```

## 命令行工具

### 可用命令

```bash
# 显示状态
python -m server cookie status

# 设置Cookie
python -m server cookie set <task> <cookie_string> [refresh_token]

# 启用/禁用任务
python -m server cookie enable <task>
python -m server cookie disable <task>

# 刷新Cookie
python -m server cookie refresh <task>
python -m server cookie refresh-all

# 测试Cookie
python -m server cookie test <task>

# 显示帮助
python -m server cookie help
```

### 示例

```bash
# 查看所有任务状态
python -m server cookie status

# 为user任务设置Cookie
python -m server cookie set user "SESSDATA=abc123;bili_jct=def456;..." "refresh_token_xyz"

# 启用user任务
python -m server cookie enable user

# 测试user任务的Cookie
python -m server cookie test user

# 手动刷新user任务的Cookie
python -m server cookie refresh user
```

## 错误处理

### 常见错误

1. **pycryptodome未安装**
   ```
   解决方案：pip install pycryptodome
   ```

2. **Cookie格式错误**
   ```
   确保Cookie字符串格式正确：key1=value1;key2=value2;...
   ```

3. **refresh_token无效**
   ```
   重新获取有效的refresh_token
   ```

4. **网络连接问题**
   ```
   检查网络连接，系统会自动重试
   ```

### 日志查看

系统会记录详细的日志信息，包括：
- Cookie刷新状态
- 错误信息和重试情况
- 任务执行情况

## 安全注意事项

1. **保护Cookie信息**：Cookie包含敏感的登录信息，请妥善保管
2. **定期更新**：建议定期检查和更新Cookie配置
3. **监控日志**：定期查看日志确保系统正常运行
4. **备份配置**：建议备份cookie_config.json文件

## 故障排除

### Cookie刷新失败

1. 检查refresh_token是否有效
2. 确认网络连接正常
3. 查看日志获取详细错误信息
4. 尝试手动刷新

### 任务无法获取Cookie

1. 检查任务是否已启用
2. 确认Cookie配置正确
3. 验证Cookie格式

### 系统无法启动

1. 检查依赖是否安装完整
2. 确认配置文件格式正确
3. 查看启动日志

## 技术实现细节

### RSA加密

使用B站官方提供的RSA公钥进行OAEP加密，生成CorrespondPath用于Cookie刷新。

### 异步处理

所有网络请求和Cookie刷新操作都使用异步处理，避免阻塞主线程。

### 线程安全

使用锁机制确保配置文件的读写操作线程安全。

### 错误重试

实现了完整的错误重试机制，包括网络错误、API错误等情况的处理。
